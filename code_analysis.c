
/*
当处于ON AUTO情况下，用户在页面点击“cycle start”，开始自动加工
*/

// 当用户在界面按下按钮，发送auto_run
def RUN(self, line=0):
    if not STATUS.is_auto_mode():
        self.ensure_mode(linuxcnc.MODE_AUTO)
    if STATUS.is_auto_paused() and line == 0:
        self.cmd.auto(linuxcnc.AUTO_STEP)
        return
    elif not STATUS.is_auto_running():
        self.cmd.auto(linuxcnc.AUTO_RUN, line)

// GUI调用linuxcnc.command.auto(AUTO_RUN, start_line)，这会创建并发送EMC_TASK_PLAN_RUN消息
static int sendProgramRun(int line)
{
    EMC_TASK_PLAN_RUN emc_task_plan_run_msg;
    
    emc_task_plan_run_msg.line = line;
    sendAuto();  // 确保处于AUTO模式
    return emcCommandSend(emc_task_plan_run_msg);
}

// 任务控制emcTaskPlan接收执行EMC_TASK_PLAN_RUN，将解释器设置为READING状态
case EMC_TASK_PLAN_RUN_TYPE:
    if (!all_homed() && !no_force_homing) {
        emcOperatorError(_("Can't run a program when not homed"));
        retval = -1;
        break;
    }
    stepping = 0;
    steppingWait = 0;
    if (!taskplanopen && emcStatus->task.file[0] != 0) {
        emcTaskPlanOpen(emcStatus->task.file);  // 打开G代码文件
    }
    run_msg = (EMC_TASK_PLAN_RUN *)cmd;
    programStartLine = run_msg->line;           // 设置起始行
    emcStatus->task.interpState = EMC_TASK_INTERP::READING;
    emcStatus->task.task_paused = 0;
    retval = 0;
    break;

// readahead_reading函数循环读取和解释
void readahead_reading(void)
{
    if (interp_list.len() <= emc_task_interp_max_len) {
        readRetval = emcTaskPlanRead();        // 读取一行G代码
        if (readRetval == INTERP_OK) {
            emcStatus->task.readLine = emcTaskPlanLine();
            emcTaskPlanCommand((char *)&emcStatus->task.command);
            execRetval = emcTaskPlanExecute(0); // 执行G代码解释
        }
    }
}

// emcTaskPlanExecute执行interp.execute
int emcTaskPlanExecute(const char *command)
{
    int retval = interp.execute(command);  // 调用解释器
    if (retval > INTERP_MIN_ERROR) {
        print_interp_error(retval);
    }
    if (command != 0) {
        FINISH();  // MDI模式需要立即完成
    }
    return retval;
}

// canon函数转换成NML运动命令，并添加到interp_list队列中
void STRAIGHT_FEED(int line_number, double x, double y, double z, 
                   double a, double b, double c, double u, double v, double w)
{
    // 坐标变换：程序坐标 → 机器坐标
    from_prog(x,y,z,a,b,c,u,v,w);
    
    // 应用旋转和偏移
    rotate_and_offset_pos(x,y,z,a,b,c,u,v,w);
    
    // 处理运动段
    see_segment(line_number, _tag, x, y, z, a, b, c, u, v, w);
}

//emcTaskExecute从interp_list获取指令
case EMC_TASK_EXEC::DONE:
    if (!emcStatus->motion.traj.queueFull &&
        emcStatus->task.interpState != EMC_TASK_INTERP::PAUSED) {
        if (0 == emcTaskCommand) {
            // 从队列获取新命令
            emcTaskCommand = interp_list.get();
            if (0 != emcTaskCommand) {
                emcStatus->task.currentLine = interp_list.get_line_number();
                emcTrajSetMotionId(emcStatus->task.currentLine);
                emcStatus->task.execState = emcTaskCheckPreconditions(emcTaskCommand.get());
            }
        }
    }


// emcTaskCheckPreconditions进行前置条件判断
static EMC_TASK_EXEC emcTaskCheckPreconditions(NMLmsg *cmd)
{
    switch (cmd->type) {
    case EMC_TRAJ_LINEAR_MOVE_TYPE:
    case EMC_TRAJ_CIRCULAR_MOVE_TYPE:
        // 运动命令需要等待IO完成
        return EMC_TASK_EXEC::WAITING_FOR_IO;
        
    case EMC_SPINDLE_ON_TYPE:
    case EMC_COOLANT_FLOOD_ON_TYPE:
        // IO命令可以立即执行
        return EMC_TASK_EXEC::DONE;
        
    case EMC_AUX_INPUT_WAIT_TYPE:
        // 需要等待运动和IO都完成
        return EMC_TASK_EXEC::WAITING_FOR_MOTION_AND_IO;
        
    default:
        return EMC_TASK_EXEC::DONE;
    }
}

// emcTaskExecute进入WAITING_FOR_IO状态，
case EMC_TASK_EXEC::WAITING_FOR_IO:
		STEPPING_CHECK();
		if (emcStatus->io.status == RCS_STATUS::ERROR)
		{
			// emcOperatorError(0, "error in IO controller");
			emcStatus->task.execState = EMC_TASK_EXEC::ERROR;
		}
		else if (emcStatus->io.status == RCS_STATUS::DONE)
		{
			emcStatus->task.execState = EMC_TASK_EXEC::DONE;
			emcTaskEager = 1;
		}
		break;

// 全局状态指向IO状态
emcStatus->io = &emcioStatus; 
RCS_STATUS::DONE (1)：操作完成，可以处理下一个命令
RCS_STATUS::EXEC (2)：正在执行操作，需要等待
RCS_STATUS::ERROR (3)：操作出错，需要错误处理

// IO状态在主循环中更新
void Task::run() {  // 每个周期调用
    // 读取工具相关的HAL输入并更新状态
    tool_status = read_tool_inputs();
    
    // 检查急停状态
    if (iocontrol_data.emc_enable_in == 0) 
        emcioStatus.aux.estop = 1;
    else
        emcioStatus.aux.estop = 0;
}

int Task::read_tool_inputs(void) {
    // 工具准备完成
    if (iocontrol_data.tool_prepare && iocontrol_data.tool_prepared) {
        emcioStatus.status = RCS_STATUS::DONE;  // 设置IO完成
        return 10;
    }
    
    // 工具更换完成
    if (iocontrol_data.tool_change && iocontrol_data.tool_changed) {
        emcioStatus.status = RCS_STATUS::DONE;  // 设置IO完成
        return 11;
    }
    
    return 0;  // 无状态变化
}

// emcTaskExecute进入DONE状态
case EMC_TASK_EXEC::DONE:
    if (0 != emcTaskCommand) {
        // 有待执行的命令，立即执行
        if (0 != emcTaskIssueCommand(emcTaskCommand.get())) {
            emcStatus->task.execState = EMC_TASK_EXEC::ERROR;
        } else {
            // 执行成功，检查后置条件
            emcStatus->task.execState = emcTaskCheckPostconditions(emcTaskCommand.get());
        }
        emcTaskCommand = 0;  // 清除当前命令
    }
    break;

// emcTaskIssueCommand处理不同类型的命令，发送给共享内存
static int emcTaskIssueCommand(NMLmsg *cmd)
{
    switch (cmd->type) {
    case EMC_TRAJ_LINEAR_MOVE_TYPE:
        emcTrajUpdateTag(((EMC_TRAJ_LINEAR_MOVE *)cmd)->tag);
        emcTrajLinearMoveMsg = (EMC_TRAJ_LINEAR_MOVE *)cmd;
        retval = emcTrajLinearMove(emcTrajLinearMoveMsg->end,
                                   emcTrajLinearMoveMsg->type, 
                                   emcTrajLinearMoveMsg->vel,
                                   emcTrajLinearMoveMsg->ini_maxvel, 
                                   emcTrajLinearMoveMsg->acc,
                                   emcTrajLinearMoveMsg->indexer_jnum);
        break;
        
    case EMC_TRAJ_CIRCULAR_MOVE_TYPE:
        // 类似处理圆弧运动
        break;
        
    case EMC_SPINDLE_ON_TYPE:
        // 处理主轴命令
        break;
    }
}

// emcTrajLinearMove将指令封装成运动控制指令
int emcTrajLinearMove(EmcPose end, int type, double vel, double ini_maxvel, double acc,
                      int indexer_jnum)
{
    // 创建运动控制器命令
    emcmotCommand.command = EMCMOT_SET_LINE;
    emcmotCommand.pos = end;                    // 目标位置
    emcmotCommand.id = TrajConfig.MotionId;     // 运动ID
    emcmotCommand.tag = localEmcTrajTag;        // 标签
    emcmotCommand.motion_type = type;           // 运动类型
    emcmotCommand.vel = vel;                    // 速度
    emcmotCommand.ini_maxvel = ini_maxvel;      // 初始最大速度
    emcmotCommand.acc = acc;                    // 加速度
    emcmotCommand.indexer_jnum = indexer_jnum;  // 索引器关节号
    
    // 发送到运动控制器
    return usrmotWriteEmcmotCommand(&emcmotCommand);
}

// 运动控制通过共享内存接收到新运动指令，添加到tp队列中
void emcmotCommandHandler_locked(void *arg, long servo_period)
case EMCMOT_SET_LINE:
    /* 添加直线运动到轨迹规划器 */
    if (!GET_MOTION_ENABLE_FLAG()) {
        reportError(_("motion stopped by enable input"));
        break;
    }
    
    // 添加到轨迹规划器
    retval = tpAddLine(&emcmotInternal->coord_tp, 
                       emcmotCommand.pos,
                       emcmotCommand.motion_type,
                       emcmotCommand.vel,
                       emcmotCommand.ini_maxvel,
                       emcmotCommand.acc,
                       emcmotStatus);
    
    if (retval < 0) {
        reportError(_("can't add line"));
    } else {
        emcmotStatus->commandEcho = emcmotCommand.command;
        emcmotStatus->commandNumEcho = emcmotCommand.commandNum;
    }
    break;
}

// 在轨迹规划中检查电机使能引脚motion.enable是否为真
static void check_for_faults(void)
{
    if (GET_MOTION_ENABLE_FLAG() != 0) {
        if (*(emcmot_hal_data->enable) == 0) {
            reportError(_("motion stopped by enable input"));
            emcmotInternal->enabling = 0;
        }
    }
}



/*
当处于ON AUTO情况下，用户在页面点击“cycle start”，开始自动加工，再次按下暂停
*/
// 当按下暂停按钮，GUI发送EMC_TASK_PLAN_PAUSE_TYPE指令
// emcTaskPlan接收处理EMC_TASK_PLAN_PAUSE_TYPE
case EMC_TASK_PLAN_PAUSE_TYPE:
    // 暂停运动控制器
    emcTrajPause();
    
    // 保存当前解释器状态（用于恢复）
    if (emcStatus->task.interpState != EMC_TASK_INTERP::PAUSED) {
        interpResumeState = emcStatus->task.interpState;
    }
    
    // 设置暂停状态
    emcStatus->task.interpState = EMC_TASK_INTERP::PAUSED;
    emcStatus->task.task_paused = 1;
    retval = 0;
    break;

// emcTaskExecute处于DONE状态，停止获取新G指令
case EMC_TASK_EXEC::DONE:
    // 检查解释器状态，如果暂停则不获取新命令
    if (!emcStatus->motion.traj.queueFull &&
        emcStatus->task.interpState != EMC_TASK_INTERP::PAUSED) {
        // 只有在非暂停状态下才从interp_list获取命令
        emcTaskCommand = interp_list.get();
    }

// 任务控制器向运动控制器发送暂停指令
int emcTrajPause() {
    emcmotCommand.command = EMCMOT_PAUSE;
    return usrmotWriteEmcmotCommand(&emcmotCommand);
}

// 运动控制器读取执行暂停指令，设置tp->pausing = 1
case EMCMOT_PAUSE:
    /* 暂停运动 */
    rtapi_print_msg(RTAPI_MSG_DBG, "PAUSE");
    tpPause(&emcmotInternal->coord_tp);  // 暂停轨迹规划器
    emcmotStatus->paused = 1;            // 设置暂停标志
    break;


// 梯形加减速
void tpCalculateTrapezoidalAccel(TP_STRUCT const *const tp, TC_STRUCT *const tc, 
                                TC_STRUCT const *const nexttc,
                                double *const acc, double *const vel_desired)
{
    // 获取当前段的目标速度（暂停时会被设为0）
    double tc_target_vel = tpGetRealTargetVel(tp, tc);
    
    // 获取最终速度（暂停时为0）
    double tc_finalvel = tpGetRealFinalVel(tp, tc, nexttc);
    
    // 计算剩余距离
    double dx = tcGetDistanceToGo(tc, tp->reverse_run);
    
    // 获取最大加速度（用于减速）
    double maxaccel = tcGetTangentialMaxAccel(tc);
    
    // 梯形速度规划公式
    double discr_term1 = pmSq(tc_finalvel);  // 最终速度的平方
    double discr_term2 = maxaccel * (2.0 * dx - tc->currentvel * tc->cycle_time);
    double tmp_adt = maxaccel * tc->cycle_time * 0.5;
    double discr_term3 = pmSq(tmp_adt);
    
    double discr = discr_term1 + discr_term2 + discr_term3;
    
    // 计算本周期的目标速度
    double maxnewvel = -tmp_adt;
    if (discr >= 0.0) {
        maxnewvel += sqrt(discr);
    }
    
    // 限制速度不超过目标速度
    *vel_desired = rtapi_fmin(maxnewvel, tc_target_vel);
    
    // 计算加速度（通常为负值，即减速）
    *acc = (*vel_desired - tc->currentvel) / tc->cycle_time;
}

// tp->pausing后，速度计算乘以倍率0，目标速度为0，做减速运动
// tpCalculateTrapezoidalAccel → tpGetRealFinalVel → tpGetFeedScale
STATIC double tpGetFeedScale(TP_STRUCT const *const tp,
                             TC_STRUCT const *const tc)
{
    if (!tc)
    {
        return 0.0;
    }
    // All reasons to disable feed override go here
    bool pausing = tp->pausing && (tc->synchronized == TC_SYNC_NONE || tc->synchronized == TC_SYNC_VELOCITY);
    bool aborting = tp->aborting;
    if (pausing)
    {
        tc_debug_print("pausing\n");
        return 0.0;
    }
    else if (aborting)
    {
        tc_debug_print("aborting\n");
        return 0.0;
    }
    else if (tc->synchronized == TC_SYNC_POSITION)
    {
        return 1.0;
    }
    else if (tc->is_blending)
    {
        // KLUDGE: Don't allow feed override to keep blending from overruning max velocity
        return fmin(emcmotStatus->net_feed_scale, 1.0);
    }
    else
    {
        return emcmotStatus->net_feed_scale;
    }
}

// 更新插补位置
int tcUpdateDistFromAccel(TC_STRUCT *const tc, double acc, double vel_desired, int reverse_run)
{
    // 更新当前速度
    tc->currentvel = vel_desired;
    
    // 计算本周期的位移
    double displacement = tc->currentvel * tc->cycle_time;
    
    // 更新运动进度
    if (reverse_run) {
        tc->progress -= displacement;
    } else {
        tc->progress += displacement;
    }
    
    // 检查是否到达终点
    if (tc->progress >= tc->target) {
        tc->currentvel = 0.0;  // 强制停止
        tc->remove = 1;        // 标记为完成
    }
    
    return TP_ERR_OK;
}

// 输出位置
// 在tpUpdateCycle中
EmcPose displacement;
tcGetPos(tc, &displacement);           // 计算位置增量
emcPoseSelfSub(&displacement, &before); // 计算本周期位移

// 更新全局位置
tpAddCurrentPos(tp, &displacement);    // tp->currentPos += displacement

// 在control.c主循环中输出到HAL
joint->motor_pos_cmd = tp->currentPos.tran.x;  // X轴位置命令
joint->vel_cmd = current_vel;                   // 速度命令（逐渐减为0）


// 检查是否停止
// 在tpCheckEndCondition中
if (tc->currentvel <= 0.0 || tc->progress >= tc->target) {
    tc->currentvel = 0.0;  // 确保速度为0
    tc->remove = 1;        // 标记段完成
    
    // 从队列移除完成的段
    tcqRemove(&tp->queue, 0);
}

// 暂停后状态
// 轨迹规划状态，队列中可能仍有未执行的运动段
tp->pausing = 1;           // 仍为暂停状态
tp->aborting = 0;          // 非中止状态
tp->done = 0;              // 未完成（因为还有未执行的段）
tp->currentPos = {精确停止位置};  // 保持精确位置
// 运动控制器状态
emcmotStatus->paused = 1;        // 暂停标志
emcmotStatus->current_vel = 0.0; // 当前速度为0
emcmotStatus->inpos = 1;         // 位置到达标志
emcmotStatus->tcqlen = N;        // 队列中剩余段数
// 关节状态
// 每个关节的状态
joint[i].motor_pos_cmd = 停止位置;  // 位置命令保持
joint[i].vel_cmd = 0.0;           // 速度命令为0
joint[i].acc_cmd = 0.0;           // 加速度命令为0
joint[i].active = 1;              // 伺服仍然活跃



/*
当处于ON AUTO情况下，用户在页面点击“cycle start”，开始自动加工，再次按下暂停，再次按下开始
*/
// 当用户按下恢复按钮时，GUI首先检查系统状态
// 在GUI中的检查
if (!emc.stat().paused) {
    // 系统未暂停，不能恢复
    return;
}

if (emc.stat().task_mode != linuxcnc.MODE_AUTO) {
    // 不在AUTO模式，不能恢复
    return;
}

// 在Python GUI中（如Axis或gmoccapy）
def resume_program():
    if emc.stat().paused and emc.stat().task_mode == linuxcnc.MODE_AUTO:
        // 系统确实处于暂停状态，可以恢复
        emc.command().auto(linuxcnc.AUTO_RESUME)

// 通过NML向任务控制器发送EMC_TASK_PLAN_RESUME_TYPE指令
// emcTaskPlan读取指令
case EMC_TASK_PLAN_RESUME_TYPE:
    // 1. 恢复运动控制器
    emcTrajResume();
    
    // 2. 恢复解释器状态
    emcStatus->task.interpState = interpResumeState;  // 通常是READING
    
    // 3. 清除暂停标志
    emcStatus->task.task_paused = 0;
    
    // 4. 清除单步执行标志
    stepping = 0;
    steppingWait = 0;
    
    retval = 0;
    break;

// 向运动控制器发送EMCMOT_RESUME指令
int emcTrajResume() {
    emcmotCommand.command = EMCMOT_RESUME;
    return usrmotWriteEmcmotCommand(&emcmotCommand);
}

// 运动控制器执行EMCMOT_RESUME指令
case EMCMOT_RESUME:
    rtapi_print_msg(RTAPI_MSG_DBG, "RESUME");
    
    // 1. 清除单步标志
    emcmotStatus->stepping = 0;
    
    // 2. 恢复轨迹规划器
    tpResume(&emcmotInternal->coord_tp);
    
    // 3. 清除暂停标志
    emcmotStatus->paused = 0;
    break;

// 恢复轨迹规划器
int tpResume(TP_STRUCT *const tp) {
    if (0 == tp) {
        return TP_ERR_FAIL;
    }
    tp->pausing = 0;  // 清除暂停标志，允许正常处理
    return TP_ERR_OK;
}


/*
当用户在页面按下急停按钮时
*/
// tpRunCycle 处理暂停
int tpRunCycle(TP_STRUCT *const tp, long period)
{
    TC_STRUCT *tc = tcqItem(&tp->queue, 0);  // 获取当前运动段
    TC_STRUCT *nexttc = tcqItem(&tp->queue, 1);  // 获取下一个运动段
    
    if (!tc) {
        tpHandleEmptyQueue(tp);  // 队列为空
        return TP_ERR_WAITING;
    }
    
    // 处理暂停和中止（关键函数）
    if (tpHandleAbort(tp, tc, nexttc) == TP_ERR_STOPPED) {
        return TP_ERR_STOPPED;
    }
    
    // 继续处理当前运动段
    // ...
}

// tpHandleAbort 处理暂停
STATIC tp_err_t tpHandleAbort(TP_STRUCT *const tp, TC_STRUCT *const tc,
                              TC_STRUCT *const nexttc)
{
    if (!tp->aborting) {
        // 暂停状态下，aborting通常为0，所以不进入中止处理
        return TP_ERR_NO_ACTION;
    }
    
    // 检查运动是否已经停止
    if (tc->currentvel == 0.0 && (!nexttc || nexttc->currentvel == 0.0)) {
        // 所有运动段都已停止，可以完全停止
        tcqInit(&tp->queue);        // 清空队列
        tp->goalPos = tp->currentPos;  // 设置目标位置为当前位置
        tp->done = 1;               // 标记完成
        tp->aborting = 0;           // 清除中止标志
        tpResume(tp);               // 清除暂停标志
        return TP_ERR_STOPPED;
    }
    
    return TP_ERR_SLOWING;  // 仍在减速过程中
}



/*
当用户在页面按下单步执行时
*/
// GUI检查当前状态
// 在Python GUI中（如Axis或gmoccapy）
def step_program():
    if emc.stat().task_mode == linuxcnc.MODE_AUTO:
        // 系统处于AUTO模式，可以单步执行
        emc.command().auto(linuxcnc.AUTO_STEP)

// 任务控制器处理单步状态
// 在不同解释器状态下的单步处理

// 1. IDLE状态 - 首次单步执行
case EMC_TASK_PLAN_STEP_TYPE:
    // 启动程序执行
    taskPlanRunCmd.line = 0;  // 从开始运行
    retval = emcTaskIssueCommand(&taskPlanRunCmd);
    if (retval != 0) break;
    
    // 立即暂停
    emcTrajPause();
    if (emcStatus->task.interpState != EMC_TASK_INTERP::PAUSED) {
        interpResumeState = emcStatus->task.interpState;
    }
    emcStatus->task.interpState = EMC_TASK_INTERP::PAUSED;
    emcStatus->task.task_paused = 1;
    break;

// 2. READING状态 - 解释器正在读取
case EMC_TASK_PLAN_STEP_TYPE:
    stepping = 1;        // 设置单步模式
    steppingWait = 0;    // 清除等待标志
    break;

// 3. PAUSED状态 - 已暂停，执行单步
case EMC_TASK_PLAN_STEP_TYPE:
    stepping = 1;
    steppingWait = 0;
    if (emcStatus->motion.traj.paused && emcStatus->motion.traj.queue > 0) {
        // 有暂停的运动，单步执行运动
        emcTrajStep();
    } else {
        // 恢复解释器状态
        emcStatus->task.interpState = interpResumeState;
    }
    emcStatus->task.task_paused = 1;
    break;

// 向运动控制器发送单步指令
int emcTrajStep()
{
    emcmotCommand.command = EMCMOT_STEP;

    return usrmotWriteEmcmotCommand(&emcmotCommand);
}

// 运动控制器处理单步指令
case EMCMOT_STEP:
    rtapi_print_msg(RTAPI_MSG_DBG, "STEP");
    if (emcmotStatus->paused) {
        // 保存当前运动ID作为单步基准
        emcmotInternal->idForStep = emcmotStatus->id;
        
        // 设置单步标志
        emcmotStatus->stepping = 1;
        
        // 临时恢复轨迹规划器（允许执行）
        tpResume(&emcmotInternal->coord_tp);
        
        // 保持暂停状态（用于后续检查）
        emcmotStatus->paused = 1;
    } else {
        reportError(_("MOTION: can't STEP while already executing"));
    }
    break;

// 单步逻辑检查
#define STEPPING_CHECK()                                    \
    if (stepping)                                           \
    {                                                       \
        if (!steppingWait)                                  \
        {                                                   \
            steppingWait = 1;                               \
            steppedLine = emcStatus->task.currentLine;      \
        }                                                   \
        else                                                \
        {                                                   \
            if (emcStatus->task.currentLine != steppedLine) \
            {                                               \
                break;  /* 行号变化，停止执行 */              \
            }                                               \
        }                                                   \
    }

// 在tpAddSegmentToQueue中为每个运动段分配ID
STATIC inline int tpAddSegmentToQueue(TP_STRUCT *const tp, TC_STRUCT *const tc, int inc_id)
{
    tc->id = tp->nextId;  // 分配当前ID
    if (tcqPut(&tp->queue, tc) == -1) {
        return TP_ERR_FAIL;
    }
    if (inc_id) {
        tp->nextId++;  // 递增ID，为下一段准备
    }
    return TP_ERR_OK;
}

// 单步执行的详细时序
// 第1次单步命令
idForStep = 100;  // 保存当前ID
stepping = 1;     // 设置单步标志

// 执行段100
while (tc->id == 100) {
    // id未变化，继续执行段100
    tpUpdateCycle();  // 更新位置和速度
    // ... 段100执行过程
}

// 段100完成，激活段101
tp->execId = 101;
emcmotStatus->id = 101;

// 下个周期检查
if (stepping && idForStep != id) {  // 100 != 101
    tpPause();     // 暂停
    stepping = 0;  // 清除标志
    paused = 1;    // 设置暂停
}

// 关键逻辑检查（在下一个周期id发生改变，暂停运动）
if (emcmotStatus->stepping && emcmotInternal->idForStep != emcmotStatus->id)
{
    // 检测到运动ID变化，触发单步暂停
    tpPause(&emcmotInternal->coord_tp);
    emcmotStatus->stepping = 0;  // 清除单步标志
    emcmotStatus->paused = 1;    // 设置暂停状态
}