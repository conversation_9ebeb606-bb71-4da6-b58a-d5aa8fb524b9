
/*
当处于ON AUTO情况下，用户在页面点击“cycle start”，开始自动加工
*/

// 当用户在界面按下按钮，发送auto_run
def RUN(self, line=0):
    if not STATUS.is_auto_mode():
        self.ensure_mode(linuxcnc.MODE_AUTO)
    if STATUS.is_auto_paused() and line == 0:
        self.cmd.auto(linuxcnc.AUTO_STEP)
        return
    elif not STATUS.is_auto_running():
        self.cmd.auto(linuxcnc.AUTO_RUN, line)

// GUI调用linuxcnc.command.auto(AUTO_RUN, start_line)，这会创建并发送EMC_TASK_PLAN_RUN消息
static int sendProgramRun(int line)
{
    EMC_TASK_PLAN_RUN emc_task_plan_run_msg;
    
    emc_task_plan_run_msg.line = line;
    sendAuto();  // 确保处于AUTO模式
    return emcCommandSend(emc_task_plan_run_msg);
}

// 任务控制emcTaskPlan接收执行EMC_TASK_PLAN_RUN，将解释器设置为READING状态
case EMC_TASK_PLAN_RUN_TYPE:
    if (!all_homed() && !no_force_homing) {
        emcOperatorError(_("Can't run a program when not homed"));
        retval = -1;
        break;
    }
    stepping = 0;
    steppingWait = 0;
    if (!taskplanopen && emcStatus->task.file[0] != 0) {
        emcTaskPlanOpen(emcStatus->task.file);  // 打开G代码文件
    }
    run_msg = (EMC_TASK_PLAN_RUN *)cmd;
    programStartLine = run_msg->line;           // 设置起始行
    emcStatus->task.interpState = EMC_TASK_INTERP::READING;
    emcStatus->task.task_paused = 0;
    retval = 0;
    break;

// readahead_reading函数循环读取和解释
void readahead_reading(void)
{
    if (interp_list.len() <= emc_task_interp_max_len) {
        readRetval = emcTaskPlanRead();        // 读取一行G代码
        if (readRetval == INTERP_OK) {
            emcStatus->task.readLine = emcTaskPlanLine();
            emcTaskPlanCommand((char *)&emcStatus->task.command);
            execRetval = emcTaskPlanExecute(0); // 执行G代码解释
        }
    }
}

// emcTaskPlanExecute执行interp.execute
int emcTaskPlanExecute(const char *command)
{
    int retval = interp.execute(command);  // 调用解释器
    if (retval > INTERP_MIN_ERROR) {
        print_interp_error(retval);
    }
    if (command != 0) {
        FINISH();  // MDI模式需要立即完成
    }
    return retval;
}

// canon函数转换成NML运动命令，并添加到interp_list队列中


//emcTaskExecute处理队列中的命令
case EMC_TASK_EXEC::DONE:
    if (!emcStatus->motion.traj.queueFull &&
        emcStatus->task.interpState != EMC_TASK_INTERP::PAUSED) {
        if (0 == emcTaskCommand) {
            // 从队列获取新命令
            emcTaskCommand = interp_list.get();
            if (0 != emcTaskCommand) {
                emcStatus->task.currentLine = interp_list.get_line_number();
                emcTrajSetMotionId(emcStatus->task.currentLine);
                emcStatus->task.execState = emcTaskCheckPreconditions(emcTaskCommand.get());
            }
        }
    }

// emcTaskPlan调用相应的运动函数

// 运动控制通过共享内存接收到新运动指令，添加到tp队列中
void emcmotCommandHandler_locked(void *arg, long servo_period)
{
    case EMCMOT_SET_LINE:
        /* append it to the emcmotInternal->coord_tp */
        tpSetId(&emcmotInternal->coord_tp, emcmotCommand->id);
        int res_addline = tpAddLine(&emcmotInternal->coord_tp,
                                    emcmotCommand->pos,
                                    emcmotCommand->motion_type,
                                    emcmotCommand->vel,
                                    emcmotCommand->ini_maxvel,
                                    emcmotCommand->acc,
                                    emcmotStatus->enables_new,
                                    issue_atspeed,
                                    emcmotCommand->turn,
                                    emcmotCommand->tag);
}

// 

if (GET_MOTION_ENABLE_FLAG() != 0) {
    if (*(emcmot_hal_data->enable) == 0) {
        reportError(_("motion stopped by enable input"));
        emcmotInternal->enabling = 0;
    }
}